<?php
require_once __DIR__ . '/../config.php';

class IntouchPayPayment {
    private $username;
    private $partnerPassword;
    private $accountId;
    private $baseUrl;
    
    public function __construct() {
        $this->username = INTOUCHPAY_USERNAME;
        $this->partnerPassword = INTOUCHPAY_PARTNER_PASSWORD;
        $this->accountId = INTOUCHPAY_ACCOUNT_ID;
        $this->baseUrl = INTOUCHPAY_BASE_URL;
    }
    
    /**
     * Generate password for IntouchPay API requests
     * Formula: SHA256(username + accountno + partnerpassword + timestamp)
     */
    private function generatePassword($timestamp) {
        $string = $this->username . $this->accountId . $this->partnerPassword . $timestamp;
        return hash('sha256', $string);
    }
    
    /**
     * Generate timestamp in required format (yyyymmddhhmmss)
     */
    private function generateTimestamp() {
        return date('YmdHis');
    }
    
    /**
     * Make HTTP POST request to IntouchPay API
     */
    private function makeRequest($url, $data) {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, INTOUCHPAY_TIMEOUT);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'error' => 'CURL Error: ' . $error
            ];
        }
        
        if ($httpCode !== 200) {
            return [
                'success' => false,
                'error' => 'HTTP Error: ' . $httpCode
            ];
        }
        
        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'error' => 'Invalid JSON response: ' . $response
            ];
        }
        
        return $decodedResponse;
    }
    
    /**
     * Validate mobile phone number for MTN/Airtel Rwanda
     * MTN: 078*, 079*, 088*
     * Airtel: 072*, 073*
     * IntouchPay expects format: 250785971082 (12 digits with country code)
     */
    private function validatePhoneNumber($phone) {
        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Check if it's a valid Rwanda mobile number (10 digits starting with 07)
        if (strlen($phone) === 10) {
            $prefix = substr($phone, 0, 3);
            // MTN: 078, 079, 088 | Airtel: 072, 073
            if (in_array($prefix, ['078', '079', '088', '072', '073'])) {
                // Convert to international format: 0782223451 -> 250782223451
                return '250' . substr($phone, 1);
            }
        }

        // Check if it's already in international format (12 digits starting with 250)
        if (strlen($phone) === 12 && substr($phone, 0, 3) === '250') {
            $prefix = substr($phone, 3, 2); // Get 2 digits after 250
            // Check for valid prefixes: 78, 79, 88, 72, 73
            if (in_array($prefix, ['78', '79', '88', '72', '73'])) {
                return $phone;
            }
        }

        return false;
    }

    /**
     * Determine mobile network provider from phone number
     */
    public function getNetworkProvider($phone) {
        $validPhone = $this->validatePhoneNumber($phone);
        if (!$validPhone) {
            return false;
        }

        // For format 250782223451, get digits 4-5 (78)
        $prefix = substr($validPhone, 3, 2); // Get first 2 digits after 250

        if (in_array($prefix, ['78', '79', '88'])) {
            return 'MTN';
        } elseif (in_array($prefix, ['72', '73'])) {
            return 'Airtel';
        }

        return false;
    }
    
    /**
     * Request payment from customer (Receiving Payment)
     */
    public function requestPayment($data) {
        // Validate required fields
        $required = ['amount', 'mobilephone', 'requesttransactionid'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return [
                    'success' => false,
                    'error' => "Missing required field: $field"
                ];
            }
        }
        
        // Validate phone number
        $validPhone = $this->validatePhoneNumber($data['mobilephone']);
        if (!$validPhone) {
            return [
                'success' => false,
                'error' => 'Invalid mobile phone number'
            ];
        }
        
        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            return [
                'success' => false,
                'error' => 'Invalid amount'
            ];
        }
        
        $timestamp = $this->generateTimestamp();
        $password = $this->generatePassword($timestamp);
        
        $requestData = [
            'username' => $this->username,
            'timestamp' => $timestamp,
            'amount' => $data['amount'],
            'password' => $password,
            'mobilephone' => $validPhone,
            'requesttransactionid' => $data['requesttransactionid'],
            'accountno' => $this->accountId,
            'callbackurl' => isset($data['callbackurl']) ? $data['callbackurl'] : INTOUCHPAY_CALLBACK_URL
        ];
        
        return $this->makeRequest(INTOUCHPAY_REQUEST_PAYMENT_URL, $requestData);
    }
    
    /**
     * Send payment to customer (Sending Payment)
     */
    public function requestDeposit($data) {
        // Validate required fields
        $required = ['amount', 'mobilephone', 'requesttransactionid'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return [
                    'success' => false,
                    'error' => "Missing required field: $field"
                ];
            }
        }
        
        // Validate phone number
        $validPhone = $this->validatePhoneNumber($data['mobilephone']);
        if (!$validPhone) {
            return [
                'success' => false,
                'error' => 'Invalid mobile phone number'
            ];
        }
        
        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            return [
                'success' => false,
                'error' => 'Invalid amount'
            ];
        }
        
        $timestamp = $this->generateTimestamp();
        $password = $this->generatePassword($timestamp);
        
        $requestData = [
            'username' => $this->username,
            'timestamp' => $timestamp,
            'amount' => $data['amount'],
            'withdrawcharge' => isset($data['withdrawcharge']) ? $data['withdrawcharge'] : 1,
            'reason' => isset($data['reason']) ? $data['reason'] : 'Payment from FreshRefund Africa',
            'sid' => isset($data['sid']) ? $data['sid'] : 1,
            'password' => $password,
            'mobilephone' => $validPhone,
            'requesttransactionid' => $data['requesttransactionid'],
            'accountno' => $this->accountId
        ];
        
        return $this->makeRequest(INTOUCHPAY_REQUEST_DEPOSIT_URL, $requestData);
    }

    /**
     * Process automated refund via IntouchPay deposit API
     * This sends money back to customer's mobile money account
     */
    public function processAutomatedRefund($phoneNumber, $amount, $refundId, $reason = 'Automated refund') {
        try {
            // Format phone number
            $validPhone = $this->validatePhoneNumber($phoneNumber);
            if (!$validPhone) {
                return [
                    'success' => false,
                    'message' => 'Invalid phone number for refund'
                ];
            }

            // Prepare refund data
            $refundData = [
                'amount' => $amount,
                'mobilephone' => $validPhone,
                'requesttransactionid' => 'REFUND_' . $refundId . '_' . time(),
                'reason' => $reason,
                'withdrawcharge' => 0, // No charge for refunds
                'sid' => 1 // Service ID
            ];

            // Execute deposit (refund) request
            $result = $this->requestDeposit($refundData);

            if (isset($result['success']) && $result['success']) {
                return [
                    'success' => true,
                    'transaction_id' => $result['transaction_id'] ?? $refundData['requesttransactionid'],
                    'amount_refunded' => $amount,
                    'phone_number' => $validPhone,
                    'message' => 'Mobile money refund processed successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['error'] ?? 'IntouchPay refund failed'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'IntouchPay refund error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check refund transaction status
     */
    public function checkRefundStatus($requestTransactionId, $intouchpayTransactionId = null) {
        try {
            $result = $this->getTransactionStatus($requestTransactionId, $intouchpayTransactionId);

            if ($result['success']) {
                return [
                    'success' => true,
                    'status' => $result['status'],
                    'data' => $result
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to check refund status'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error checking refund status: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get transaction status
     */
    public function getTransactionStatus($requestTransactionId, $transactionId) {
        if (empty($requestTransactionId) || empty($transactionId)) {
            return [
                'success' => false,
                'error' => 'Missing transaction IDs'
            ];
        }
        
        $timestamp = $this->generateTimestamp();
        $password = $this->generatePassword($timestamp);
        
        $requestData = [
            'username' => $this->username,
            'timestamp' => $timestamp,
            'password' => $password,
            'requesttransactionid' => $requestTransactionId,
            'transactionid' => $transactionId
        ];
        
        return $this->makeRequest(INTOUCHPAY_GET_TRANSACTION_STATUS_URL, $requestData);
    }
    
    /**
     * Get account balance
     */
    public function getBalance() {
        $timestamp = $this->generateTimestamp();
        $password = $this->generatePassword($timestamp);
        
        $requestData = [
            'username' => $this->username,
            'timestamp' => $timestamp,
            'accountno' => $this->accountId,
            'password' => $password
        ];
        
        return $this->makeRequest(INTOUCHPAY_GET_BALANCE_URL, $requestData);
    }
    
    /**
     * Test withdrawal functionality
     * Sends money from IntouchPay account to mobile money account
     */
    public function testWithdrawal($phoneNumber, $amount, $reason = 'Test withdrawal') {
        // Validate phone number
        $validPhone = $this->validatePhoneNumber($phoneNumber);
        if (!$validPhone) {
            return [
                'success' => false,
                'error' => 'Invalid phone number for withdrawal'
            ];
        }

        // Generate unique transaction ID for test
        $requestTransactionId = 'TEST_WITHDRAW_' . time() . '_' . rand(1000, 9999);

        $withdrawalData = [
            'amount' => $amount,
            'mobilephone' => $validPhone,
            'requesttransactionid' => $requestTransactionId,
            'reason' => $reason,
            'withdrawcharge' => 0, // No charge for test
            'sid' => 1 // Service ID for bulk payments
        ];

        return $this->requestDeposit($withdrawalData);
    }

    /**
     * Get response code description
     */
    public function getResponseCodeDescription($code) {
        $codes = [
            '1000' => 'Pending',
            '01' => 'Successful',
            '0002' => 'Missing Username Information',
            '0003' => 'Missing Password Information',
            '0004' => 'Missing Date Information',
            '0005' => 'Invalid Password',
            '0006' => 'User Does not have an intouchPay Account',
            '0007' => 'No such user',
            '0008' => 'Failed to Authenticate',
            '2100' => 'Amount should be greater than 0',
            '2200' => 'Amount below minimum',
            '2300' => 'Amount above maximum',
            '2400' => 'Duplicate Transaction ID',
            '2500' => 'Route Not Found',
            '2600' => 'Operation Not Allowed',
            '2700' => 'Failed to Complete Transaction',
            '1005' => 'Failed Due to Insufficient Funds',
            '1002' => 'Mobile number not registered on mobile money',
            '1008' => 'General Failure',
            '1200' => 'Invalid Number',
            '1100' => 'Number not supported on this Mobile money network',
            '1300' => 'Failed to Complete Transaction, Unknown Exception',
            '2001' => 'Request Successful',
            '2003' => 'Transaction Not Allowed',
            '2102' => 'Subscriber Could not be Identified',
            '2105' => 'Non Existent Mobile Account',
            '2106' => 'Own Mobile Account Provided',
            '2107' => 'Invalid Amount Format',
            '2108' => 'Insufficient Funds on Source Account',
            '2109' => 'Daily Limit Exceeded',
            '2110' => 'Source Account Not Active',
            '2111' => 'Mobile Account Not Active',
            '2000' => 'General Failure',
            '2510' => 'Service Temporarily Unavailable',
            '2518' => 'Could Not Perform Operation',
            '2520' => 'Incorrect Account Password',
            '2522' => 'Invalid Amount',
            '2525' => 'Resource Not Active',
            '2600' => 'Network Failure - Request Timed Out',
            '2800' => 'Deposit Channel Failure',
            '3000' => 'Missing Transaction ID Information',
            '3200' => 'Missing Request Transaction ID Information',
            '3100' => 'Transaction Doesn\'t Exist'
        ];
        
        return isset($codes[$code]) ? $codes[$code] : 'Unknown response code';
    }

    /**
     * Process automatic withdrawal for customer refunds
     * Same as testWithdrawal but optimized for automated refund processing
     */
    public function processAutomaticWithdrawal($phoneNumber, $amount, $reason = 'Automatic refund') {
        try {
            // Validate phone number
            $cleanPhone = $this->validatePhoneNumber($phoneNumber);
            if (!$cleanPhone) {
                return [
                    'success' => false,
                    'error' => 'Invalid phone number: ' . $phoneNumber,
                    'responsecode' => '1102'
                ];
            }

            // Check balance before processing
            $balanceResult = $this->getBalance();
            if (!$balanceResult['success'] || $balanceResult['balance'] < $amount) {
                return [
                    'success' => false,
                    'error' => 'Insufficient balance. Available: RWF ' . number_format($balanceResult['balance'] ?? 0, 2),
                    'responsecode' => '1108'
                ];
            }

            // Generate request data
            $timestamp = $this->generateTimestamp();
            $password = $this->generatePassword($timestamp);
            $requestTransactionId = 'AUTO_' . time() . '_' . rand(1000, 9999);

            $requestData = [
                'username' => $this->username,
                'accountno' => $this->accountId,
                'amount' => $amount,
                'mobilephone' => $cleanPhone,
                'withdrawcharge' => 0, // No charge for refunds
                'reason' => $reason,
                'sid' => 1, // Service ID for bulk payments
                'timestamp' => $timestamp,
                'password' => $password,
                'requesttransactionid' => $requestTransactionId
            ];

            // Make the withdrawal request
            $response = $this->makeRequest(INTOUCHPAY_REQUEST_DEPOSIT_URL, $requestData);

            // Process response
            if (isset($response['responsecode'])) {
                if ($response['responsecode'] == '2001') {
                    // Success
                    return [
                        'success' => true,
                        'message' => 'Money sent successfully to ' . $phoneNumber,
                        'transaction_id' => $response['requesttransactionid'] ?? $requestTransactionId,
                        'amount' => $amount,
                        'phone_number' => $phoneNumber,
                        'responsecode' => $response['responsecode'],
                        'intouchpay_response' => $response
                    ];
                } else {
                    // Error response
                    $errorMessages = [
                        '1108' => 'Insufficient account balance',
                        '1102' => 'Invalid mobile phone number',
                        '2105' => 'Non-existent mobile account',
                        '2000' => 'General failure'
                    ];

                    $errorMessage = $errorMessages[$response['responsecode']] ?? 'Unknown error';

                    return [
                        'success' => false,
                        'error' => $errorMessage,
                        'responsecode' => $response['responsecode'],
                        'intouchpay_response' => $response
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'error' => 'Invalid response from IntouchPay',
                    'responsecode' => 'INVALID_RESPONSE',
                    'intouchpay_response' => $response
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception during automatic withdrawal: ' . $e->getMessage(),
                'responsecode' => 'EXCEPTION'
            ];
        }
    }
}
?>
