<?php
/**
 * Instant Refund Processor - Processes money refunds within 30 seconds
 * Integrates with automated_refund_engine.php for complete automation
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/automated_refund_engine.php';

class InstantRefundProcessor {
    private $conn;
    private $refundEngine;
    private $logFile;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->refundEngine = new AutomatedRefundEngine($database_connection);
        $this->logFile = __DIR__ . '/../logs/instant_refunds.log';
        
        // Ensure logs directory exists
        if (!file_exists(dirname($this->logFile))) {
            mkdir(dirname($this->logFile), 0755, true);
        }
    }
    
    /**
     * Process instant refund when refund request is approved
     * This is called automatically when admin approves or system auto-approves
     */
    public function processInstantRefund($refundRequestId) {
        $this->log("Processing instant refund for request ID: $refundRequestId");
        
        try {
            // Get refund request details
            $refundRequest = $this->getRefundRequestDetails($refundRequestId);
            
            if (!$refundRequest) {
                throw new Exception("Refund request not found");
            }
            
            // Check if already processed
            if ($refundRequest['status'] !== 'approved') {
                throw new Exception("Refund request not approved for processing");
            }
            
            // Create refund record if not exists
            $refundId = $this->createRefundRecord($refundRequest);
            
            // Process automated refund
            $result = $this->refundEngine->processAutomatedRefund($refundId);
            
            if ($result['success']) {
                // Update refund request status
                $this->updateRefundRequestStatus($refundRequestId, 'completed', 'Refund processed automatically');
                
                $this->log("Instant refund completed for request $refundRequestId. Transaction: " . $result['transaction_id']);
                
                return [
                    'success' => true,
                    'message' => 'Refund processed instantly',
                    'refund_id' => $refundId,
                    'transaction_id' => $result['transaction_id'],
                    'amount' => $refundRequest['amount'],
                    'processing_time' => $result['processing_time'] ?? 0
                ];
            } else {
                // Update refund request as failed
                $this->updateRefundRequestStatus($refundRequestId, 'failed', $result['message']);
                
                return [
                    'success' => false,
                    'message' => $result['message']
                ];
            }
            
        } catch (Exception $e) {
            $this->log("Instant refund error for request $refundRequestId: " . $e->getMessage());
            $this->updateRefundRequestStatus($refundRequestId, 'failed', $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Auto-approve and process refunds based on business rules
     */
    public function autoProcessEligibleRefunds() {
        $this->log("Starting auto-processing of eligible refunds");
        
        // Get pending refund requests that meet auto-approval criteria
        $eligibleRefunds = $this->getEligibleRefundsForAutoProcessing();
        
        $processed = 0;
        $failed = 0;
        
        foreach ($eligibleRefunds as $refund) {
            try {
                // Auto-approve the refund
                $this->updateRefundRequestStatus($refund['id'], 'approved', 'Auto-approved by system');
                
                // Process instant refund
                $result = $this->processInstantRefund($refund['id']);
                
                if ($result['success']) {
                    $processed++;
                    $this->log("Auto-processed refund request {$refund['id']} successfully");
                } else {
                    $failed++;
                    $this->log("Failed to auto-process refund request {$refund['id']}: " . $result['message']);
                }
                
                // Small delay to prevent overwhelming the APIs
                usleep(500000); // 0.5 seconds
                
            } catch (Exception $e) {
                $failed++;
                $this->log("Error auto-processing refund request {$refund['id']}: " . $e->getMessage());
            }
        }
        
        $this->log("Auto-processing completed. Processed: $processed, Failed: $failed");
        
        return [
            'processed' => $processed,
            'failed' => $failed,
            'total_eligible' => count($eligibleRefunds)
        ];
    }
    
    /**
     * Get refund request details
     */
    private function getRefundRequestDetails($refundRequestId) {
        $sql = "SELECT rr.*, o.transaction_id, o.payment_method, c.fullname, c.email, c.phone
                FROM refund_requests rr
                JOIN orders o ON rr.order_id = o.id
                JOIN customers c ON rr.customer_id = c.id
                WHERE rr.id = ?";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $refundRequestId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->fetch_assoc();
    }
    
    /**
     * Create refund record from approved refund request
     */
    private function createRefundRecord($refundRequest) {
        // Check if refund record already exists
        $checkSql = "SELECT id FROM refunds WHERE order_id = ? AND customer_id = ? AND amount = ?";
        $stmt = $this->conn->prepare($checkSql);
        $stmt->bind_param("iid", $refundRequest['order_id'], $refundRequest['customer_id'], $refundRequest['amount']);
        $stmt->execute();
        $existing = $stmt->get_result()->fetch_assoc();
        
        if ($existing) {
            return $existing['id'];
        }
        
        // Create new refund record
        $sql = "INSERT INTO refunds (
                    order_id, customer_id, amount, reason, status, 
                    refund_request_id, created_at
                ) VALUES (?, ?, ?, ?, 'pending', ?, NOW())";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("iidsi", 
            $refundRequest['order_id'],
            $refundRequest['customer_id'],
            $refundRequest['amount'],
            $refundRequest['reason'],
            $refundRequest['id']
        );
        $stmt->execute();
        
        return $this->conn->insert_id;
    }
    
    /**
     * Update refund request status
     */
    public function updateRefundRequestStatus($refundRequestId, $status, $notes = '') {
        $sql = "UPDATE refund_requests SET 
                status = ?, 
                admin_notes = ?,
                updated_at = NOW()
                WHERE id = ?";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ssi", $status, $notes, $refundRequestId);
        $stmt->execute();
    }
    
    /**
     * Get refunds eligible for auto-processing
     */
    private function getEligibleRefundsForAutoProcessing() {
        $sql = "SELECT rr.*, o.created_at as order_date, o.payment_method
                FROM refund_requests rr
                JOIN orders o ON rr.order_id = o.id
                WHERE rr.status = 'pending'
                AND (
                    -- Auto-approve if order is within 5 minutes
                    TIMESTAMPDIFF(MINUTE, o.created_at, NOW()) <= 5
                    OR
                    -- Auto-approve for specific reasons
                    rr.reason IN ('not_delivered', 'damaged_product', 'wrong_product')
                    OR
                    -- Auto-approve small amounts (under 10,000 RWF)
                    rr.amount < 10000
                )
                ORDER BY rr.created_at ASC
                LIMIT 50"; // Process max 50 at a time
                
        $result = $this->conn->query($sql);
        return $result->fetch_all(MYSQLI_ASSOC);
    }
    
    /**
     * Process refund queue - called by cron job
     */
    public function processRefundQueue() {
        $this->log("Processing refund queue");
        
        // Process auto-eligible refunds
        $autoResult = $this->autoProcessEligibleRefunds();
        
        // Process manually approved refunds
        $manualResult = $this->processApprovedRefunds();
        
        return [
            'auto_processed' => $autoResult,
            'manual_processed' => $manualResult
        ];
    }
    
    /**
     * Process manually approved refunds
     */
    private function processApprovedRefunds() {
        $sql = "SELECT id FROM refund_requests 
                WHERE status = 'approved' 
                AND id NOT IN (SELECT COALESCE(refund_request_id, 0) FROM refunds WHERE status = 'completed')
                ORDER BY updated_at ASC
                LIMIT 20";
                
        $result = $this->conn->query($sql);
        $approvedRefunds = $result->fetch_all(MYSQLI_ASSOC);
        
        $processed = 0;
        $failed = 0;
        
        foreach ($approvedRefunds as $refund) {
            $result = $this->processInstantRefund($refund['id']);
            
            if ($result['success']) {
                $processed++;
            } else {
                $failed++;
            }
            
            // Small delay
            usleep(500000); // 0.5 seconds
        }
        
        return [
            'processed' => $processed,
            'failed' => $failed,
            'total_approved' => count($approvedRefunds)
        ];
    }
    
    /**
     * Log processor activities
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}

// CLI usage for cron jobs
if (php_sapi_name() === 'cli' && isset($argv[1]) && $argv[1] === 'process-queue') {
    $processor = new InstantRefundProcessor($conn);
    $result = $processor->processRefundQueue();
    echo "Refund queue processed: " . json_encode($result) . "\n";
}
?>
