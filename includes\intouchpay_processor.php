<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/intouchpay_payment.php';

class IntouchPayProcessor {
    private $conn;
    private $intouchpay;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->intouchpay = new IntouchPayPayment();
    }
    
    /**
     * Process payment request for an order
     */
    public function processPaymentRequest($orderId, $customerId, $phoneNumber, $paymentMethod = 'mtn') {
        try {
            // Get order details
            $stmt = $this->conn->prepare("SELECT * FROM orders WHERE id = ? AND customer_id = ?");
            $stmt->bind_param("ii", $orderId, $customerId);
            $stmt->execute();
            $order = $stmt->get_result()->fetch_assoc();
            $stmt->close();
            
            if (!$order) {
                return [
                    'success' => false,
                    'error' => 'Order not found'
                ];
            }
            
            // Generate unique request transaction ID
            $requestTransactionId = 'FRA_' . $orderId . '_' . time() . '_' . rand(1000, 9999);
            
            // Prepare payment request data
            $paymentData = [
                'amount' => $order['total_amount'],
                'mobilephone' => $phoneNumber,
                'requesttransactionid' => $requestTransactionId,
                'callbackurl' => INTOUCHPAY_CALLBACK_URL
            ];
            
            // Start database transaction
            $this->conn->begin_transaction();
            
            try {
                // Insert IntouchPay transaction record
                $stmt = $this->conn->prepare("
                    INSERT INTO intouchpay_transactions 
                    (order_id, customer_id, request_transaction_id, amount, mobile_phone, payment_type, status) 
                    VALUES (?, ?, ?, ?, ?, 'request_payment', 'pending')
                ");
                $stmt->bind_param("iisds", $orderId, $customerId, $requestTransactionId, $order['total_amount'], $phoneNumber);
                $stmt->execute();
                $intouchpayTransactionId = $this->conn->insert_id;
                $stmt->close();
                
                // Make payment request to IntouchPay
                $response = $this->intouchpay->requestPayment($paymentData);
                
                if (isset($response['success']) && $response['success']) {
                    // Update IntouchPay transaction with response
                    $stmt = $this->conn->prepare("
                        UPDATE intouchpay_transactions 
                        SET intouchpay_transaction_id = ?, response_code = ?, response_message = ? 
                        WHERE id = ?
                    ");
                    $responseCode = isset($response['responsecode']) ? $response['responsecode'] : null;
                    $responseMessage = isset($response['message']) ? $response['message'] : 'Payment request initiated';
                    $intouchpayTxnId = isset($response['transactionid']) ? $response['transactionid'] : null;
                    
                    $stmt->bind_param("sssi", $intouchpayTxnId, $responseCode, $responseMessage, $intouchpayTransactionId);
                    $stmt->execute();
                    $stmt->close();
                    
                    // Insert/Update payment record
                    $stmt = $this->conn->prepare("
                        INSERT INTO payments 
                        (order_id, customer_id, amount, payment_method, payment_provider, intouchpay_request_id, intouchpay_transaction_id, status, response_code) 
                        VALUES (?, ?, ?, ?, 'intouchpay', ?, ?, 'pending', ?)
                        ON DUPLICATE KEY UPDATE 
                        intouchpay_request_id = VALUES(intouchpay_request_id),
                        intouchpay_transaction_id = VALUES(intouchpay_transaction_id),
                        response_code = VALUES(response_code)
                    ");
                    $stmt->bind_param("iidssss", $orderId, $customerId, $order['total_amount'], $paymentMethod, $requestTransactionId, $intouchpayTxnId, $responseCode);
                    $stmt->execute();
                    $stmt->close();
                    
                    // Update order status
                    $stmt = $this->conn->prepare("UPDATE orders SET payment_status = 'pending', payment_method = ? WHERE id = ?");
                    $stmt->bind_param("si", $paymentMethod, $orderId);
                    $stmt->execute();
                    $stmt->close();
                    
                    $this->conn->commit();
                    
                    return [
                        'success' => true,
                        'message' => 'Payment request sent successfully',
                        'request_transaction_id' => $requestTransactionId,
                        'intouchpay_transaction_id' => $intouchpayTxnId,
                        'status' => 'pending',
                        'response' => $response
                    ];
                    
                } else {
                    // Update transaction with error
                    $errorMessage = isset($response['error']) ? $response['error'] : 'Payment request failed';
                    $responseCode = isset($response['responsecode']) ? $response['responsecode'] : 'ERROR';
                    
                    $stmt = $this->conn->prepare("
                        UPDATE intouchpay_transactions 
                        SET status = 'failed', response_code = ?, response_message = ? 
                        WHERE id = ?
                    ");
                    $stmt->bind_param("ssi", $responseCode, $errorMessage, $intouchpayTransactionId);
                    $stmt->execute();
                    $stmt->close();
                    
                    $this->conn->commit();
                    
                    return [
                        'success' => false,
                        'error' => $errorMessage,
                        'response_code' => $responseCode,
                        'response' => $response
                    ];
                }
                
            } catch (Exception $e) {
                $this->conn->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Handle callback from IntouchPay
     */
    public function handleCallback($callbackData) {
        try {
            // Log the callback
            $stmt = $this->conn->prepare("
                INSERT INTO intouchpay_callback_logs 
                (request_transaction_id, intouchpay_transaction_id, callback_data, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $requestTxnId = isset($callbackData['requesttransactionid']) ? $callbackData['requesttransactionid'] : null;
            $intouchpayTxnId = isset($callbackData['transactionid']) ? $callbackData['transactionid'] : null;
            $callbackJson = json_encode($callbackData);
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            
            $stmt->bind_param("sssss", $requestTxnId, $intouchpayTxnId, $callbackJson, $ipAddress, $userAgent);
            $stmt->execute();
            $callbackLogId = $this->conn->insert_id;
            $stmt->close();
            
            if (!$requestTxnId) {
                return [
                    'success' => false,
                    'error' => 'Missing request transaction ID in callback'
                ];
            }
            
            // Find the transaction
            $stmt = $this->conn->prepare("SELECT * FROM intouchpay_transactions WHERE request_transaction_id = ?");
            $stmt->bind_param("s", $requestTxnId);
            $stmt->execute();
            $transaction = $stmt->get_result()->fetch_assoc();
            $stmt->close();
            
            if (!$transaction) {
                return [
                    'success' => false,
                    'error' => 'Transaction not found'
                ];
            }
            
            // Start database transaction
            $this->conn->begin_transaction();
            
            try {
                $status = isset($callbackData['status']) ? strtolower($callbackData['status']) : 'failed';
                $responseCode = isset($callbackData['responsecode']) ? $callbackData['responsecode'] : null;
                $statusDesc = isset($callbackData['statusdesc']) ? $callbackData['statusdesc'] : null;
                
                // Store previous status to check if payment just became successful
                $previousStatus = $transaction['status'];

                // Determine final status
                $finalStatus = 'failed';
                $orderStatus = 'failed';
                $paymentStatus = 'failed';

                if ($status === 'successful' || $responseCode === '01') {
                    $finalStatus = 'successful';
                    $orderStatus = 'processing';
                    $paymentStatus = 'completed';
                } elseif ($status === 'pending' || $responseCode === '1000') {
                    $finalStatus = 'pending';
                    $orderStatus = 'pending';
                    $paymentStatus = 'pending';
                }

                // Update IntouchPay transaction
                $stmt = $this->conn->prepare("
                    UPDATE intouchpay_transactions
                    SET status = ?, response_code = ?, response_message = ?, callback_data = ?,
                        callback_received_at = NOW(), completed_at = NOW()
                    WHERE id = ?
                ");
                $stmt->bind_param("ssssi", $finalStatus, $responseCode, $statusDesc, $callbackJson, $transaction['id']);
                $stmt->execute();
                $stmt->close();

                // Update payments table
                $stmt = $this->conn->prepare("
                    UPDATE payments
                    SET status = ?, callback_received = TRUE, response_code = ?, response_message = ?
                    WHERE intouchpay_request_id = ?
                ");
                $stmt->bind_param("ssss", $paymentStatus, $responseCode, $statusDesc, $requestTxnId);
                $stmt->execute();
                $stmt->close();

                // Update order
                $stmt = $this->conn->prepare("
                    UPDATE orders
                    SET status = ?, payment_status = ?, transaction_id = ?
                    WHERE id = ?
                ");
                $stmt->bind_param("sssi", $orderStatus, $paymentStatus, $intouchpayTxnId, $transaction['order_id']);
                $stmt->execute();
                $stmt->close();

                // Mark callback as processed
                $stmt = $this->conn->prepare("UPDATE intouchpay_callback_logs SET processed = TRUE WHERE id = ?");
                $stmt->bind_param("i", $callbackLogId);
                $stmt->execute();
                $stmt->close();

                // If payment just became successful, send email notification
                if ($previousStatus !== 'successful' && $finalStatus === 'successful') {
                    $this->sendPaymentSuccessEmail($transaction['order_id']);
                }

                $this->conn->commit();
                
                return [
                    'success' => true,
                    'message' => 'Callback processed successfully',
                    'status' => $finalStatus,
                    'order_id' => $transaction['order_id']
                ];
                
            } catch (Exception $e) {
                $this->conn->rollback();
                
                // Log error in callback log
                $stmt = $this->conn->prepare("UPDATE intouchpay_callback_logs SET error_message = ? WHERE id = ?");
                $errorMsg = $e->getMessage();
                $stmt->bind_param("si", $errorMsg, $callbackLogId);
                $stmt->execute();
                $stmt->close();
                
                throw $e;
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Callback processing error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Check transaction status
     */
    public function checkTransactionStatus($requestTransactionId) {
        try {
            $stmt = $this->conn->prepare("SELECT * FROM intouchpay_transactions WHERE request_transaction_id = ?");
            $stmt->bind_param("s", $requestTransactionId);
            $stmt->execute();
            $transaction = $stmt->get_result()->fetch_assoc();
            $stmt->close();

            if (!$transaction) {
                return [
                    'success' => false,
                    'error' => 'Transaction not found'
                ];
            }

            $previousStatus = $transaction['status'];

            // If we have IntouchPay transaction ID, check status with API
            if ($transaction['intouchpay_transaction_id']) {
                $response = $this->intouchpay->getTransactionStatus(
                    $requestTransactionId,
                    $transaction['intouchpay_transaction_id']
                );

                if (isset($response['success']) && $response['success']) {
                    // Update local status if needed
                    $apiStatus = isset($response['status']) ? strtolower($response['status']) : 'pending';
                    $responseCode = isset($response['responsecode']) ? $response['responsecode'] : null;

                    if ($apiStatus !== $transaction['status']) {
                        $stmt = $this->conn->prepare("
                            UPDATE intouchpay_transactions
                            SET status = ?, response_code = ?, updated_at = NOW()
                            WHERE id = ?
                        ");
                        $stmt->bind_param("ssi", $apiStatus, $responseCode, $transaction['id']);
                        $stmt->execute();
                        $stmt->close();

                        $transaction['status'] = $apiStatus;
                        $transaction['response_code'] = $responseCode;

                        // If payment just became successful, send email notification
                        if ($previousStatus !== 'successful' && $apiStatus === 'successful') {
                            $this->sendPaymentSuccessEmail($transaction['order_id']);
                        }
                    }
                }
            }

            return [
                'success' => true,
                'transaction' => $transaction,
                'api_response' => isset($response) ? $response : null
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Status check error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send payment success email notification
     */
    private function sendPaymentSuccessEmail($orderId) {
        try {
            // Get order and customer details
            $stmt = $this->conn->prepare("
                SELECT o.*, c.fullname, c.email
                FROM orders o
                JOIN customers c ON o.customer_id = c.id
                WHERE o.id = ?
            ");
            $stmt->bind_param("i", $orderId);
            $stmt->execute();
            $orderData = $stmt->get_result()->fetch_assoc();
            $stmt->close();

            if ($orderData) {
                // Include email notifications class
                require_once __DIR__ . '/email_notifications.php';
                $emailNotifications = new EmailNotifications();

                // Send payment success email
                $result = $emailNotifications->sendPaymentSuccessEmail(
                    $orderData['email'],
                    $orderData['fullname'],
                    $orderData['total_amount'],
                    $orderId
                );

                // Log the email sending result
                if ($result['success']) {
                    error_log("Payment success email sent for order ID: $orderId");
                } else {
                    error_log("Failed to send payment success email for order ID: $orderId - " . $result['message']);
                }
            }
        } catch (Exception $e) {
            error_log("Error sending payment success email for order ID: $orderId - " . $e->getMessage());
        }
    }
}
?>
