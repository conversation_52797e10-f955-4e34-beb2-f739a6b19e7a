<?php
require_once(__DIR__ . '/../config.php');
require_once(__DIR__ . '/../vendor/autoload.php'); // For PHPMailer
use P<PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

class RefundHandler {
    private $conn;
    private $mailer;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->initializeMailer();
    }
    
    private function initializeMailer() {
        $this->mailer = new PHPMailer(true);
        
        // Server settings
        $this->mailer->isSMTP();
        $this->mailer->Host = 'freshrefund.gmail.com'; // Update with your SMTP host
        $this->mailer->SMTPAuth = true;
        $this->mailer->Username = '<EMAIL>'; // Update with your email
        $this->mailer->Password = 'eios wmac cslo dqfu'; // Update with your app password
        $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $this->mailer->Port = 587;
        
        // Default sender
        $this->mailer->setFrom('<EMAIL>', 'FreshRefund Africa');
    }
    
    public function processUndeliveredRefund($orderId, $userId) {
        try {
            // Start transaction
            $this->conn->begin_transaction();
            
            // Get order details
            $stmt = $this->conn->prepare("
                SELECT o.*, c.email, c.fullname 
                FROM orders o 
                JOIN customers c ON o.customer_id = c.id 
                WHERE o.id = ? AND o.customer_id = ?
            ");
            $stmt->bind_param("ii", $orderId, $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $order = $result->fetch_assoc();
            
            if (!$order) {
                throw new Exception("Order not found");
            }
            
            // Check if order is eligible for automatic refund
            if (!in_array($order['status'], ['pending', 'processing', 'shipped'])) {
                throw new Exception("Order is not eligible for automatic refund");
            }
            
            // Process refund through Flutterwave
            $refundAmount = $order['total_amount'];
            $refundResult = $this->processFlutterwaveRefund($order['transaction_id'], $refundAmount);
            
            if (!$refundResult['success']) {
                throw new Exception("Refund processing failed: " . $refundResult['message']);
            }
            
            // Update order status
            $updateStmt = $this->conn->prepare("
                UPDATE orders 
                SET status = 'refunded', 
                    refund_status = 'completed',
                    refund_date = NOW(),
                    refund_transaction_id = ?
                WHERE id = ?
            ");
            $updateStmt->bind_param("si", $refundResult['refund_id'], $orderId);
            $updateStmt->execute();
            
            // Create refund record
            $refundStmt = $this->conn->prepare("
                INSERT INTO refunds (
                    order_id, 
                    customer_id, 
                    amount, 
                    reason, 
                    status, 
                    refund_transaction_id,
                    created_at
                ) VALUES (?, ?, ?, ?, 'completed', ?, NOW())
            ");
            $reason = "Automatic refund for undelivered order";
            $refundStmt->bind_param("iidss", $orderId, $userId, $refundAmount, $reason, $refundResult['refund_id']);
            $refundStmt->execute();
            
            // Send email notification
            $this->sendRefundConfirmationEmail($order);
            
            // Commit transaction
            $this->conn->commit();
            
            return [
                'success' => true,
                'message' => 'Refund processed successfully'
            ];
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->conn->rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function processFlutterwaveRefund($transactionId, $amount) {
        // Initialize Flutterwave API
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => FLUTTERWAVE_API_URL . "/transactions/" . $transactionId . "/refund",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode([
                'amount' => $amount
            ]),
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . FLUTTERWAVE_SECRET_KEY,
                "Content-Type: application/json"
            ],
        ]);
        
        $response = curl_exec($curl);
        $err = curl_error($curl);
        
        curl_close($curl);
        
        if ($err) {
            return [
                'success' => false,
                'message' => "cURL Error: " . $err
            ];
        }
        
        $result = json_decode($response, true);
        
        if ($result['status'] === 'success') {
            return [
                'success' => true,
                'refund_id' => $result['data']['id']
            ];
        }
        
        return [
            'success' => false,
            'message' => $result['message'] ?? 'Refund processing failed'
        ];
    }
    
    private function sendRefundConfirmationEmail($order) {
        try {
            $this->mailer->addAddress($order['email'], $order['fullname']);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Your Refund Has Been Processed - FreshRefund Africa';
            
            // Email template
            $body = "
                <h2>Refund Confirmation</h2>
                <p>Dear {$order['fullname']},</p>
                <p>We have processed your refund for order #{$order['id']}.</p>
                <p><strong>Refund Details:</strong></p>
                <ul>
                    <li>Order Number: #{$order['id']}</li>
                    <li>Refund Amount: RWF " . number_format($order['total_amount'], 2) . "</li>
                    <li>Refund Date: " . date('Y-m-d H:i:s') . "</li>
                </ul>
                <p>The refunded amount will be credited to your original payment method within 5-7 business days.</p>
                <p>Thank you for choosing FreshRefund Africa.</p>
                <p>Best regards,<br>FreshRefund Africa Team</p>
            ";
            
            $this->mailer->Body = $body;
            $this->mailer->send();
            
            return true;
        } catch (Exception $e) {
            // Log email error but don't stop the refund process
            error_log("Failed to send refund confirmation email: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * Refund Handler Functions
 * Contains helper functions for processing refunds
 */

/**
 * Calculate refund amount based on order status and time
 * @param float $original_amount Original order amount
 * @param string $order_status Current order status
 * @param string $delivery_date Date when order was delivered
 * @return array Array containing refund amount and any applicable fees
 */
function calculate_refund_amount($original_amount, $order_status, $delivery_date) {
    $refund_amount = $original_amount;
    $fee = 0.00;
    
    if ($order_status === 'delivered') {
        $delivery_timestamp = strtotime($delivery_date);
        $current_date = time();
        $days_since_delivery = floor(($current_date - $delivery_timestamp) / (60 * 60 * 24));
        
        // Apply 10% fee for refunds after 15 days
        if ($days_since_delivery > 15) {
            $fee = $original_amount * 0.10;
            $refund_amount = $original_amount - $fee;
        }
    }
    
    return [
        'refund_amount' => $refund_amount,
        'fee' => $fee
    ];
}

/**
 * Validate refund eligibility
 * @param array $order Order details
 * @return array Array containing eligibility status and message
 */
function validate_refund_eligibility($order) {
    if (!in_array($order['status'], ['pending', 'processing', 'shipped', 'delivered'])) {
        return [
            'eligible' => false,
            'message' => 'Order status does not allow refunds'
        ];
    }
    
    if ($order['status'] === 'delivered') {
        $delivery_date = strtotime($order['created_at']);
        $current_date = time();
        $days_since_delivery = floor(($current_date - $delivery_date) / (60 * 60 * 24));
        
        if ($days_since_delivery > 30) {
            return [
                'eligible' => false,
                'message' => 'Refund period has expired (30 days from delivery)'
            ];
        }
    }
    
    return [
        'eligible' => true,
        'message' => 'Order is eligible for refund'
    ];
}

/**
 * Process refund payment
 * @param array $refund Refund details
 * @param string $refund_method Selected refund method
 * @return array Array containing processing status and message
 */
function process_refund_payment($refund, $refund_method) {
    // TODO: Implement actual payment processing based on refund method
    // This is a placeholder for the actual implementation
    
    switch ($refund_method) {
        case 'original_payment':
            // Process refund to original payment method
            return [
                'success' => true,
                'message' => 'Refund processed to original payment method'
            ];
            
        case 'bank_transfer':
            // Process bank transfer
            return [
                'success' => true,
                'message' => 'Refund processed via bank transfer'
            ];
            
        case 'mobile_money':
            // Process mobile money transfer
            return [
                'success' => true,
                'message' => 'Refund processed via mobile money'
            ];
            
        default:
            return [
                'success' => false,
                'message' => 'Invalid refund method'
            ];
    }
}

/**
 * Send refund notification
 * @param array $refund Refund details
 * @param array $customer Customer details
 * @param string $type Notification type (requested, approved, rejected, completed)
 * @return bool Success status of notification
 */
function send_refund_notification($refund, $customer, $type) {
    $subject = '';
    $message = '';
    
    switch ($type) {
        case 'requested':
            $subject = "Refund Request Received - Order #" . $refund['order_id'];
            $message = "
                <h2>Refund Request Received</h2>
                <p>Dear " . htmlspecialchars($customer['fullname']) . ",</p>
                <p>We have received your refund request for Order #" . $refund['order_id'] . ".</p>
                <p><strong>Refund Details:</strong></p>
                <ul>
                    <li>Order Number: #" . $refund['order_id'] . "</li>
                    <li>Amount: RWF " . number_format($refund['amount'], 2) . "</li>
                    <li>Reason: " . ucfirst($refund['reason_category']) . "</li>
                    <li>Refund Method: " . ucfirst($refund['refund_method']) . "</li>
                </ul>
                <p>We will process your refund request within 24-48 hours.</p>
            ";
            break;
            
        case 'approved':
            $subject = "Refund Request Approved - Order #" . $refund['order_id'];
            $message = "
                <h2>Refund Request Approved</h2>
                <p>Dear " . htmlspecialchars($customer['fullname']) . ",</p>
                <p>Your refund request for Order #" . $refund['order_id'] . " has been approved.</p>
                <p>We will process your refund within 3-5 business days.</p>
            ";
            break;
            
        case 'rejected':
            $subject = "Refund Request Rejected - Order #" . $refund['order_id'];
            $message = "
                <h2>Refund Request Rejected</h2>
                <p>Dear " . htmlspecialchars($customer['fullname']) . ",</p>
                <p>We regret to inform you that your refund request for Order #" . $refund['order_id'] . " has been rejected.</p>
                <p>If you have any questions, please contact our customer support.</p>
            ";
            break;
            
        case 'completed':
            $subject = "Refund Completed - Order #" . $refund['order_id'];
            $message = "
                <h2>Refund Completed</h2>
                <p>Dear " . htmlspecialchars($customer['fullname']) . ",</p>
                <p>Your refund for Order #" . $refund['order_id'] . " has been processed successfully.</p>
                <p><strong>Refund Details:</strong></p>
                <ul>
                    <li>Amount: RWF " . number_format($refund['amount'], 2) . "</li>
                    <li>Method: " . ucfirst($refund['refund_method']) . "</li>
                    <li>Date: " . date('F j, Y') . "</li>
                </ul>
            ";
            break;
    }
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= 'From: FreshRefund Africa <<EMAIL>>' . "\r\n";
    
    return mail($customer['email'], $subject, $message, $headers);
}
?> 