<?php
require_once __DIR__ . '/../config/flutterwave.php';

class FlutterwavePayment {
    private $publicKey;
    private $secretKey;
    private $encryptionKey;
    private $apiUrl;

    public function __construct() {
        $this->publicKey = FLUTTERWAVE_PUBLIC_KEY;
        $this->secretKey = FLUTTERWAVE_SECRET_KEY;
        $this->encryptionKey = FLUTTERWAVE_ENCRYPTION_KEY;
        $this->apiUrl = FLUTTERWAVE_API_URL;
    }

    public function initiatePayment($data) {
        // Validate mobile money number if provided
        if (isset($data['payment_type']) && $data['payment_type'] === 'mobilemoneyrw') {
            if (!isset($data['phone']) || !$this->isValidMobileMoneyNumber($data['phone'])) {
                return [
                    'status' => 'error',
                    'message' => 'Invalid mobile money number. Please provide a valid MTN or Airtel number.'
                ];
            }
        }
    }

    /**
     * Process automated refund via Flutterwave API
     */
    public function processAutomatedRefund($transactionId, $amount, $reason = 'Automated refund') {
        try {
            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => $this->apiUrl . "/transactions/" . $transactionId . "/refund",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode([
                    'amount' => $amount,
                    'comments' => $reason
                ]),
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer " . $this->secretKey,
                    "Content-Type: application/json"
                ],
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $err = curl_error($curl);
            curl_close($curl);

            if ($err) {
                return [
                    'success' => false,
                    'message' => "cURL Error: " . $err
                ];
            }

            $responseData = json_decode($response, true);

            if ($httpCode === 200 && isset($responseData['status']) && $responseData['status'] === 'success') {
                return [
                    'success' => true,
                    'transaction_id' => $responseData['data']['id'],
                    'amount_refunded' => $responseData['data']['amount_refunded'],
                    'refund_date' => $responseData['data']['created_at'],
                    'message' => 'Refund processed successfully via Flutterwave'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['message'] ?? 'Flutterwave refund failed'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Refund processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check refund status
     */
    public function checkRefundStatus($refundId) {
        try {
            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => $this->apiUrl . "/refunds/" . $refundId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer " . $this->secretKey,
                    "Content-Type: application/json"
                ],
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200) {
                $responseData = json_decode($response, true);
                return [
                    'success' => true,
                    'status' => $responseData['data']['status'],
                    'data' => $responseData['data']
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to check refund status'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error checking refund status: ' . $e->getMessage()
            ];
        }

        $paymentData = [
            'tx_ref' => 'FRESHREFUND_' . time() . '_' . uniqid(),
            'amount' => $data['amount'],
            'currency' => CURRENCY,
            'payment_options' => PAYMENT_METHODS,
            'redirect_url' => $data['redirect_url'],
            'customer' => [
                'email' => $data['email'],
                'name' => $data['name'],
                'phone_number' => $data['phone']
            ],
            'customizations' => [
                'title' => PAYMENT_TITLE,
                'description' => PAYMENT_DESCRIPTION,
                'logo' => 'https://your-logo-url.com/logo.png'
            ],
            'meta' => [
                'order_id' => $data['order_id'],
                'customer_id' => $data['customer_id']
            ]
        ];

        // Add mobile money specific data if payment type is mobile money
        if (isset($data['payment_type']) && $data['payment_type'] === 'mobilemoneyrw') {
            $paymentData['payment_type'] = 'mobilemoneyrw';
            $paymentData['is_mobile_money'] = 1;
        }

        $headers = [
            'Authorization: Bearer ' . $this->secretKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init($this->apiUrl . '/payments');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    private function isValidMobileMoneyNumber($phone) {
        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Check if it's a valid Rwanda mobile number
        // MTN numbers start with 07 or 2507
        // Airtel numbers start with 07 or 2507
        if (strlen($phone) === 10 && substr($phone, 0, 2) === '07') {
            return true;
        } elseif (strlen($phone) === 12 && substr($phone, 0, 4) === '2507') {
            return true;
        }
        
        return false;
    }

    public function verifyPayment($transactionId) {
        $headers = [
            'Authorization: Bearer ' . $this->secretKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init($this->apiUrl . '/transactions/' . $transactionId . '/verify');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    public function initiateRefund($data) {
        // Check refund amount limits
        if ($data['amount'] > MAX_REFUND_AMOUNT) {
            return [
                'status' => 'error',
                'message' => 'Refund amount exceeds maximum limit of RWF ' . MAX_REFUND_AMOUNT
            ];
        }

        if ($data['amount'] < MIN_REFUND_AMOUNT) {
            return [
                'status' => 'error',
                'message' => 'Refund amount is below minimum limit of RWF ' . MIN_REFUND_AMOUNT
            ];
        }

        $refundData = [
            'amount' => $data['amount'],
            'transaction_id' => $data['transaction_id'],
            'reason' => $data['reason']
        ];

        $headers = [
            'Authorization: Bearer ' . $this->secretKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init($this->apiUrl . '/refunds');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($refundData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    public function verifyRefund($refundId) {
        $headers = [
            'Authorization: Bearer ' . $this->secretKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init($this->apiUrl . '/refunds/' . $refundId);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }
}
?> 