<?php
/**
 * Language Manager for FreshMarket
 * Handles bilingual support (English & Kinyarwanda)
 */

class LanguageManager {
    private static $instance = null;
    private $currentLanguage = 'en';
    private $translations = [];
    private $supportedLanguages = [
        'en' => ['name' => 'English', 'flag' => '🇬🇧', 'code' => 'en'],
        'rw' => ['name' => 'Kinyarwanda', 'flag' => '🇷🇼', 'code' => 'rw']
    ];
    
    private function __construct() {
        $this->detectLanguage();
        $this->loadTranslations();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Detect user's preferred language
     */
    private function detectLanguage() {
        // Priority: URL parameter > Session > Cookie > Browser > Default
        
        // 1. Check URL parameter
        if (isset($_GET['lang']) && array_key_exists($_GET['lang'], $this->supportedLanguages)) {
            $this->setLanguage($_GET['lang']);
            return;
        }
        
        // 2. Check session
        if (isset($_SESSION['language']) && array_key_exists($_SESSION['language'], $this->supportedLanguages)) {
            $this->currentLanguage = $_SESSION['language'];
            return;
        }
        
        // 3. Check cookie
        if (isset($_COOKIE['language']) && array_key_exists($_COOKIE['language'], $this->supportedLanguages)) {
            $this->setLanguage($_COOKIE['language']);
            return;
        }
        
        // 4. Check browser language
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $browserLang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);
            if (array_key_exists($browserLang, $this->supportedLanguages)) {
                $this->setLanguage($browserLang);
                return;
            }
        }
        
        // 5. Default to English
        $this->currentLanguage = 'en';
    }
    
    /**
     * Set current language
     */
    public function setLanguage($language) {
        if (array_key_exists($language, $this->supportedLanguages)) {
            $this->currentLanguage = $language;
            $_SESSION['language'] = $language;
            
            // Set cookie for 30 days
            setcookie('language', $language, time() + (30 * 24 * 60 * 60), '/');
            
            // Reload translations
            $this->loadTranslations();
        }
    }
    
    /**
     * Get current language
     */
    public function getCurrentLanguage() {
        return $this->currentLanguage;
    }
    
    /**
     * Get supported languages
     */
    public function getSupportedLanguages() {
        return $this->supportedLanguages;
    }
    
    /**
     * Load translations for current language
     */
    private function loadTranslations() {
        $languageFile = __DIR__ . '/../languages/' . $this->currentLanguage . '.php';
        
        if (file_exists($languageFile)) {
            $this->translations = include $languageFile;
        } else {
            // Fallback to English
            $fallbackFile = __DIR__ . '/../languages/en.php';
            if (file_exists($fallbackFile)) {
                $this->translations = include $fallbackFile;
            }
        }
    }
    
    /**
     * Translate a key
     */
    public function translate($key, $params = []) {
        $translation = $this->translations[$key] ?? $key;
        
        // Replace parameters like {name}, {amount}, etc.
        if (!empty($params)) {
            foreach ($params as $param => $value) {
                $translation = str_replace('{' . $param . '}', $value, $translation);
            }
        }
        
        return $translation;
    }
    
    /**
     * Get all translations
     */
    public function getAllTranslations() {
        return $this->translations;
    }
    
    /**
     * Check if language is RTL (not applicable for our languages, but good to have)
     */
    public function isRTL() {
        return false; // Neither English nor Kinyarwanda are RTL
    }
    
    /**
     * Get language direction
     */
    public function getDirection() {
        return 'ltr';
    }
    
    /**
     * Get language switcher HTML
     */
    public function getLanguageSwitcher() {
        $html = '<div class="language-switcher">';
        $html .= '<select onchange="changeLanguage(this.value)" class="language-select">';
        
        foreach ($this->supportedLanguages as $code => $info) {
            $selected = ($code === $this->currentLanguage) ? 'selected' : '';
            $html .= '<option value="' . $code . '" ' . $selected . '>';
            $html .= $info['flag'] . ' ' . $info['name'];
            $html .= '</option>';
        }
        
        $html .= '</select>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Get JavaScript for language switching
     */
    public function getLanguageSwitcherJS() {
        return "
        <script>
        function changeLanguage(language) {
            // Update URL with language parameter
            const url = new URL(window.location);
            url.searchParams.set('lang', language);
            window.location.href = url.toString();
        }
        </script>
        ";
    }
    
    /**
     * Get current language info
     */
    public function getCurrentLanguageInfo() {
        return $this->supportedLanguages[$this->currentLanguage];
    }
}

/**
 * Global translation function for easy use
 */
function t($key, $params = []) {
    return LanguageManager::getInstance()->translate($key, $params);
}

/**
 * Get current language
 */
function getCurrentLang() {
    return LanguageManager::getInstance()->getCurrentLanguage();
}

/**
 * Check if current language is Kinyarwanda
 */
function isKinyarwanda() {
    return getCurrentLang() === 'rw';
}

/**
 * Check if current language is English
 */
function isEnglish() {
    return getCurrentLang() === 'en';
}

// Initialize language manager
$languageManager = LanguageManager::getInstance();
?>
