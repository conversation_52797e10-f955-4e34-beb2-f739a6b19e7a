<?php
/**
 * Test Mode Automated Refund Engine
 * Simulates money transfers for testing purposes
 */

require_once __DIR__ . "/../config.php";
require_once __DIR__ . "/../config/test_mode.php";

class TestModeRefundEngine {
    private $conn;
    private $logFile;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->logFile = TEST_LOG_FILE;
        
        // Ensure logs directory exists
        if (!file_exists(dirname($this->logFile))) {
            mkdir(dirname($this->logFile), 0755, true);
        }
    }
    
    /**
     * Simulate automated refund processing
     */
    public function processTestRefund($refundId) {
        $this->log("Starting TEST MODE refund process for ID: $refundId");
        
        try {
            // Get refund details
            $refundData = $this->getRefundDetails($refundId);
            
            if (!$refundData) {
                throw new Exception("Refund not found");
            }
            
            // Update status to processing
            $this->updateRefundStatus($refundId, "processing", "TEST MODE: Simulating refund processing");
            
            // Simulate processing time
            sleep(SIMULATE_PROCESSING_TIME);
            
            // Simulate success/failure based on configured rate
            $success = (rand(1, 100) <= SIMULATE_SUCCESS_RATE);
            
            if ($success) {
                $transactionId = TEST_REFUND_PREFIX . $refundId . "_" . time();
                
                // Update to completed
                $this->updateRefundStatus($refundId, "completed", "TEST MODE: Refund simulation completed successfully", $transactionId);
                
                // Send test notification
                $this->sendTestNotification($refundData, $transactionId);
                
                $this->log("TEST MODE: Refund $refundId completed successfully. Transaction: $transactionId");
                
                return [
                    "success" => true,
                    "message" => "TEST MODE: Refund processed successfully (simulated)",
                    "transaction_id" => $transactionId,
                    "amount" => $refundData["amount"],
                    "test_mode" => true
                ];
            } else {
                // Simulate failure
                $this->updateRefundStatus($refundId, "failed", "TEST MODE: Simulated processing failure");
                $this->log("TEST MODE: Refund $refundId failed (simulated)");
                
                return [
                    "success" => false,
                    "message" => "TEST MODE: Refund processing failed (simulated)",
                    "test_mode" => true
                ];
            }
            
        } catch (Exception $e) {
            $this->updateRefundStatus($refundId, "failed", "TEST MODE ERROR: " . $e->getMessage());
            $this->log("TEST MODE: Refund $refundId error: " . $e->getMessage());
            
            return [
                "success" => false,
                "message" => "TEST MODE ERROR: " . $e->getMessage(),
                "test_mode" => true
            ];
        }
    }
    
    private function getRefundDetails($refundId) {
        $sql = "SELECT r.*, c.fullname, c.email, c.phone, o.payment_method
                FROM refunds r
                JOIN customers c ON r.customer_id = c.id
                JOIN orders o ON r.order_id = o.id
                WHERE r.id = ?";
                
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $refundId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->fetch_assoc();
    }
    
    private function updateRefundStatus($refundId, $status, $notes = "", $transactionId = null) {
        $sql = "UPDATE refunds SET 
                status = ?, 
                notes = ?,
                updated_at = NOW()";
        
        $params = [$status, $notes];
        $types = "ss";
        
        if ($transactionId) {
            $sql .= ", refund_transaction_id = ?";
            $params[] = $transactionId;
            $types .= "s";
        }
        
        if ($status === "completed") {
            $sql .= ", completed_at = NOW()";
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $refundId;
        $types .= "i";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
    }
    
    private function sendTestNotification($refundData, $transactionId) {
        $message = "TEST MODE NOTIFICATION:\n";
        $message .= "Dear " . $refundData["fullname"] . ",\n\n";
        $message .= "Your refund has been processed successfully (TEST MODE).\n\n";
        $message .= "Amount: RWF " . number_format($refundData["amount"]) . "\n";
        $message .= "Transaction ID: " . $transactionId . "\n";
        $message .= "Payment Method: " . $refundData["payment_method"] . "\n\n";
        $message .= "NOTE: This is a TEST MODE simulation. No real money was transferred.\n";
        
        $this->log("TEST MODE: Notification sent to " . $refundData["email"]);
        $this->log("TEST MODE: SMS would be sent to " . $refundData["phone"]);
    }
    
    private function log($message) {
        $timestamp = date("Y-m-d H:i:s");
        $logMessage = "[$timestamp] $message\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}
?>