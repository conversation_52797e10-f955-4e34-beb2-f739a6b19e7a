<?php
require_once 'vendor/autoload.php';
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;

class EmailNotifications {
    private $smtp_host = 'smtp.gmail.com';
    private $smtp_port = 587;
    private $smtp_user = '<EMAIL>';  // Your Gmail address
    private $smtp_pass = 'eios wmac cslo dqfu';  // Your Gmail App Password
    private $from_email = '<EMAIL>';
    private $from_name = 'Fresh Refund';

    /**
     * Send payment success notification email
     */
    public function sendPaymentSuccessEmail($customerEmail, $customerName, $orderAmount, $orderId) {
        try {
            $mail = new PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtp_user;
            $mail->Password = $this->smtp_pass;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->smtp_port;

            // Recipients
            $mail->setFrom($this->from_email, $this->from_name);
            $mail->addAddress($customerEmail, $customerName);

            // Content
            $mail->isHTML(true);
            $mail->Subject = 'Fresh Refund';
            $mail->Body = "Dear " . htmlspecialchars($customerName) . ",<br><br>
            Your payment was successful. You are allowed to request a refund within 5 minutes if you wish.<br><br>
            <strong>Order Details:</strong><br>
            Order #: " . htmlspecialchars($orderId) . "<br>
            Amount: RWF " . number_format($orderAmount, 0, '.', ',') . "<br><br>
            <strong>Important:</strong> You have 5 minutes from the time of this payment to request a refund if needed. After this time, the refund option will no longer be available.<br><br>
            To request a refund, please visit your profile page and look for the refund option next to this order.<br><br>
            Thank you for choosing Fresh Refund!<br><br>
            Best regards,<br>
            Fresh Refund Team";

            $mail->send();
            return ['success' => true, 'message' => 'Payment success email sent successfully'];
        } catch (Exception $e) {
            error_log("Failed to send payment success email: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to send payment success email'];
        }
    }

    /**
     * Send refund success notification email
     */
    public function sendRefundSuccessEmail($customerEmail, $customerName, $refundAmount, $orderId, $paymentPhone) {
        try {
            $mail = new PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtp_user;
            $mail->Password = $this->smtp_pass;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->smtp_port;

            // Recipients
            $mail->setFrom($this->from_email, $this->from_name);
            $mail->addAddress($customerEmail, $customerName);

            // Content
            $mail->isHTML(true);
            $mail->Subject = 'Fresh Refund';
            $mail->Body = "Dear " . htmlspecialchars($customerName) . ",<br><br>
            Your refund was successfully processed.<br><br>
            <strong>Refund Details:</strong><br>
            Order #: " . htmlspecialchars($orderId) . "<br>
            Refund Amount: RWF " . number_format($refundAmount, 0, '.', ',') . "<br>
            Sent to: " . htmlspecialchars($paymentPhone) . "<br><br>
            The refund has been sent to your mobile money account. Please check your mobile money balance and transaction history.<br><br>
            If you don't see the refund in your account within a few minutes, please contact your mobile money provider or our customer support.<br><br>
            Thank you for using Fresh Refund!<br><br>
            Best regards,<br>
            Fresh Refund Team";

            $mail->send();
            return ['success' => true, 'message' => 'Refund success email sent successfully'];
        } catch (Exception $e) {
            error_log("Failed to send refund success email: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to send refund success email'];
        }
    }

    /**
     * Generic email sending function for custom messages
     */
    public function sendCustomEmail($customerEmail, $customerName, $subject, $message) {
        try {
            $mail = new PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtp_user;
            $mail->Password = $this->smtp_pass;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->smtp_port;

            // Recipients
            $mail->setFrom($this->from_email, $this->from_name);
            $mail->addAddress($customerEmail, $customerName);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $message;

            $mail->send();
            return ['success' => true, 'message' => 'Email sent successfully'];
        } catch (Exception $e) {
            error_log("Failed to send custom email: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to send email'];
        }
    }
}
?>
